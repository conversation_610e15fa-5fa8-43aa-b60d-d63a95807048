import { useState, useEffect, useContext, useRef } from "react";
import { AppProviderStore } from "../../AppStore";
import { observer } from "mobx-react";
import AudioPlayer from "../WorkViewComponents/AudioPlayer";
import { toJS } from "mobx";
import Results from "./Results";
import NewResults from "./NewResults";
import GroupInfo from "./GroupInfo";

function Review() {
    const [currentVideoIndex, setCurrentVideoIndex] = useState({});
    const [selectedClassification, setSelectedClassification] = useState(null);
    const videoRef = useRef(null);

    const { AppStore } = useContext(AppProviderStore);
    const { selectedGroup, currentTrack, setTrack, trackList, judgeIntros, competitionState } = AppStore;

    console.log('Review---', toJS(AppStore.groups), toJS(judgeIntros));
    console.log('CompetitionState', toJS(competitionState));
    console.log(toJS(selectedGroup));

    const initButtons =
        trackList.length > 1
            ? { prev: false, next: true }
            : false;
    const [toggleButtons, setButtons] = useState(initButtons);

    useEffect(() => {
        setButtons(initButtons);
    }, [selectedGroup]);

    const changeTrack = async (direction) => {
        // Prevent navigation if recording or saving a memo
        if (AppStore.isRecording) {
            alert('Please stop recording before changing tracks');
            return;
        }

        if (AppStore.isSavingMemo) {
            alert('Please wait for the memo to finish saving before changing tracks');
            return;
        }

        let currIndex = trackList.indexOf(currentTrack);
        let shiftIndex = currIndex + direction;
        let maxIndex = trackList.length - 1;

        let buttons = { ...toggleButtons };
        buttons.prev = shiftIndex > 0;
        buttons.next = shiftIndex < maxIndex;

        setTrack(trackList[shiftIndex]);
        setButtons({ ...buttons });
    };

    const currentYear = new Date().getFullYear();
    // Show Results for year 2023, otherwise NewResults for past years and GroupInfo for current year
    const showResults = selectedGroup?.year === 2023;
    const showNewResults = selectedGroup?.year !== 2023 && (selectedGroup?.year < currentYear || competitionState.resultState);

    return (
        <>
            <AudioPlayer
                group={selectedGroup}
                track={currentTrack}
                buttons={toggleButtons}
                changeTrack={changeTrack}
            />
            {showResults ? <Results /> : showNewResults ? <NewResults /> : <GroupInfo />}
        </>
    );
}

export default observer(Review);
